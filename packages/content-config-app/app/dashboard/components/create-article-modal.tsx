"use client";
import type { CreateBlogInput } from "@/app/api/manage/blog/create/route";
import { PlusCircleOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Form, Input, Modal, Radio, Typography } from "antd";
import { useState } from "react";

type FV = CreateBlogInput["blog"] & CreateBlogInput["content"];

const createArticle = async (v: FV) => {
  const blogData: CreateBlogInput = {
    blog: {
      articleDate: new Date(),
      isPremium: v.isPremium ?? false,
      isPublished: v.isPublished ?? false,
      slug: v.slug ?? "",
    },
    content: {
      title: v.title ?? "",
      imageUrl: v.imageUrl ?? "",
      seoDescription: v.seoDescription ?? "",
      content: v.content ?? "",
      language: v.language ?? "en",
    },
  };

  await fetch("/api/manage/blog/create", {
    method: "POST",
    body: JSON.stringify(blogData),
  });
};

export function CrateArticleModal() {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  return (
    <>
      <a onClick={() => setOpen(true)}>
        <PlusCircleOutlined />
      </a>

      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        okText="Create"
        okButtonProps={{ htmlType: "submit", form: "create-article-form" }}
        destroyOnHidden
      >
        <Typography.Title className="flex items-center gap-4">
          <span>Create a new article</span>
        </Typography.Title>
        <Form
          layout="vertical"
          onFinish={async (v) => {
            setOpen(false);
            await createArticle(v);
            queryClient.invalidateQueries({
              queryKey: ["articleList"],
              exact: true,
            });
          }}
          name="create-article-form"
        >
          <Form.Item<FV> name="language" initialValue={"en"}>
            <Radio.Group optionType="button">
              <Radio value="en">English</Radio>
              <Radio value="jp">日本語</Radio>
              <Radio value="zh">中文</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item<FV>
            name="title"
            label="Title"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
