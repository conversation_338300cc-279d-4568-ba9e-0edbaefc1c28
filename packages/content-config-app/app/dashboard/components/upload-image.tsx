import React, { useState } from "react";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { Input, message, Radio, Upload } from "antd";
import type { GetProp, UploadProps } from "antd";
import { ImageResize } from "ui/src/utils/image-resize";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

const beforeUpload = async (file: FileType) => {
  const isImage = ["image/jpeg", "image/png", "image/webp"].includes(file.type);
  if (!isImage) {
    message.error("You can only upload JPG/PNG/WEBP file!");
    return false;
  }

  // Check file size (10MB limit)
  if (file.size > 10 * 1024 * 1024) {
    message.error("File size must be less than 10MB!");
    return false;
  }

  const imageResize = new ImageResize({ width: 1200 });
  const resizedFile = await imageResize.resizeFile(file);

  if (!resizedFile) {
    message.error("We got something wrong with your image!");
    return false;
  }
  return resizedFile;
};

export const UploadImage = ({
  value,
  onChange,
}: {
  value?: string;
  onChange?: (value: string) => void;
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(value ?? "");

  const handleChange: UploadProps["onChange"] = (info) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      const imageUrl = info.file.response?.url;
      onChange?.(imageUrl);
      setImageUrl(imageUrl + "?v=" + Date.now());
      setLoading(false);
    }
  };

  const [mode, setMode] = useState<"upload" | "edit">("upload");

  const renderContent = () => {
    if (mode === "edit") {
      return (
        <div className="flex flex-col">
          <div className="relative border border-dashed border-gray-300 rounded-xl flex items-center justify-center flex-col w-[270px] h-[135px] overflow-hidden">
            <Input.TextArea
              style={{ width: "100%", height: "100%", resize: "none" }}
              value={value}
              onChange={(e) => {
                const imageUrl = e.target.value;
                onChange?.(e.target.value);
                setImageUrl(imageUrl + "?v=" + Date.now());
              }}
            />
          </div>
        </div>
      );
    }

    return (
      <Upload
        showUploadList={false}
        action="/api/manage/image-upload"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        className="flex flex-col"
      >
        <div className="border border-dashed border-gray-300 rounded-xl flex items-center justify-center flex-col w-[270px] h-[135px] overflow-hidden">
          {imageUrl && !loading ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={imageUrl}
              alt="banner"
              className="w-full h-full object-cover"
            />
          ) : (
            <>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div style={{ marginTop: 8 }}>Upload</div>
            </>
          )}
        </div>
      </Upload>
    );
  };

  return (
    <div className="group">
      {renderContent()}
      <Radio.Group
        className="absolute bottom-2 right-2 hidden group-hover:block"
        value={mode}
        onChange={(e) => setMode(e.target.value)}
        size="small"
      >
        <Radio.Button value="upload">Upload</Radio.Button>
        <Radio.Button value="edit">Edit</Radio.Button>
      </Radio.Group>
    </div>
  );
};

export default UploadImage;
