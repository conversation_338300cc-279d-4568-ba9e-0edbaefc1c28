import { ImageBucket } from "ui/src/utils";
import { NextResponse } from "next/server";

const { S3_BUCKET_NAME, AWS_REGION } = process.env;

export async function POST(req: Request) {
  const formData = await req.formData();

  const { searchParams } = new URL(req.url);
  const key = searchParams.get("key");

  const file = (await formData.get("file")) as File;

  if (!file) {
    return NextResponse.json({ error: "No files received." }, { status: 400 });
  }

  if (!file.type.match(/(jpg|jpeg|png|webp)$/i)) {
    return NextResponse.json(
      { error: "Only jpg,png,webp formats are allowed!" },
      { status: 400 },
    );
  }

  const fileKey = key || file.name;
  const bucket = new ImageBucket();
  await bucket.uploadFile({ key: fileKey, file, resize: true });

  return NextResponse.json({
    message: "File uploaded successfully.",
    url: `https://${S3_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com/blog-image/${fileKey}`,
  });
}
