import { BlogListResponse } from "@/app/api/manage/blog/list/route";
import { EditOutlined } from "@ant-design/icons";
import { Avatar, Tooltip } from "antd";
import { useNavigate } from "react-router-dom";

export const RenderTitle = ({ record }: { record: BlogListResponse[0] }) => {
  const navigate = useNavigate();
  const { contents } = record;

  return (
    <div className="flex items-center gap-2 relative">
      <Avatar
        shape="square"
        src={contents[0].imageUrl || null}
        className="flex-shrink-0"
      />
      <div>
        {contents.map((entry) => {
          const { title, language, id, isReady } = entry;
          const inCompleteFields = Object.entries({
            ...entry,
            ...record,
          }).filter(([, value]) => value === "");
          return (
            <div
              key={language}
              className="flex gap-1 group cursor-pointer"
              onClick={() => navigate(`/edit/${id}`)}
            >
              <div className={"line-clamp-1"}>
                <span>[{language}] </span>
                {!isReady && (
                  <span className="text-yellow-500">[draft] </span>
                )}{" "}
                {title}
              </div>
              {!!inCompleteFields.length && (
                <Tooltip
                  title={
                    <span>
                      Empty fields:{" "}
                      <b>{inCompleteFields.map(([key]) => key).join(", ")}</b>
                    </span>
                  }
                >
                  ⚠️{" "}
                </Tooltip>
              )}
              <EditOutlined className="invisible group-hover:visible" />
            </div>
          );
        })}
      </div>
    </div>
  );
};
