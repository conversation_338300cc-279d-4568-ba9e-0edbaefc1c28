import {
  DeleteObjectCommand,
  ListO<PERSON>sV2<PERSON>ommand,
  PutO<PERSON><PERSON>ommand,
  S3Client,
  S3ServiceException,
} from "@aws-sdk/client-s3";
import { Jim<PERSON> } from "jimp";

/**
 * Upload a file to an S3 bucket.
 */

const { S3_BUCKET_NAME, AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY } =
  process.env;

export class ImageBucket {
  private client: S3Client | null = null;

  private getClient(): S3Client {
    if (this.client) {
      return this.client;
    }

    if (
      !S3_BUCKET_NAME ||
      !AWS_REGION ||
      !AWS_ACCESS_KEY_ID ||
      !AWS_SECRET_ACCESS_KEY
    ) {
      throw new Error("Missing environment variables for S3 configuration");
    }

    this.client = new S3Client({
      region: AWS_REGION,
      credentials: {
        accessKeyId: AWS_ACCESS_KEY_ID,
        secretAccessKey: AWS_SECRET_ACCESS_KEY,
      },
    });

    return this.client;
  }

  listFiles = async () => {
    const command = new ListObjectsV2Command({
      Bucket: S3_BUCKET_NAME,
    });

    const response = await this.getClient().send(command);
    console.log(response);
    return response;
  };

  uploadFile = async ({
    key,
    file,
    resize = false,
  }: {
    key: string;
    file: File;
    resize: boolean;
  }) => {
    const buffer =
      file instanceof Buffer
        ? file
        : Buffer.from(await (file as any).arrayBuffer());
    await this.upload({ key: `blog-image/${key}`, buffer });

    if (!resize) return;

    // Skip resizing for WebP files since Jimp doesn't support WebP decoding
    // The original WebP file is already uploaded above
    if (file.type === 'image/webp') {
      console.log(`Skipping resize for WebP file: ${key}. Original file uploaded successfully.`);
      return;
    }

    try {
      // Load image with Jimp (works for JPEG, PNG, BMP, TIFF, GIF)
      const image = await Jimp.fromBuffer(buffer);

      // Resize to 800x800 with fit inside and convert to JPEG
      const image800 = image.clone().resize({ w: 800, h: 800 });
      const buffer800 = await image800.getBuffer("image/jpeg", { quality: 80 });
      await this.upload({
        key: `blog-image-resize-800/${key}`,
        buffer: buffer800,
      });

      // Resize to 400x400 with fit inside and convert to JPEG
      const image400 = image.clone().resize({ w: 400, h: 400 });
      const buffer400 = await image400.getBuffer("image/jpeg", { quality: 80 });
      await this.upload({
        key: `blog-image-resize-400/${key}`,
        buffer: buffer400,
      });
    } catch (error) {
      console.error(`Failed to process image ${key}:`, error);
      // Don't throw the error, just log it so the original upload still succeeds
    }
  };

  upload = async ({ key, buffer }: { key: string; buffer: Buffer }) => {
    const command = new PutObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
      Body: buffer,
    });

    try {
      const response = await this.getClient().send(command);
      console.log(response);
    } catch (caught) {
      if (
        caught instanceof S3ServiceException &&
        caught.name === "EntityTooLarge"
      ) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}. \
  The object was too large. To upload objects larger than 5GB, use the S3 console (160GB max) \
  or the multipart upload API (5TB max).`,
        );
      } else if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };

  deleteFile = async ({ key }: { key: string }) => {
    const command = new DeleteObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
    });

    try {
      const response = await this.getClient().send(command);
      console.log(response);
    } catch (caught) {
      if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while deleting object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };
}
