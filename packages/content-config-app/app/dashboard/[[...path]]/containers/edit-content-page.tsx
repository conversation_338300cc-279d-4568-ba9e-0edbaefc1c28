"use client";
import type { BlogGetResponse } from "@/app/api/manage/content/route";
import type { UpdateBlogInput } from "@/app/api/manage/blog/update/route";
import { LeftCircleOutlined } from "@ant-design/icons";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button, Checkbox, Form, Input, Radio, Select, Typography } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import UploadImage from "../../components/upload-image";
import { ContentField } from "../../components/content-field";
import { ImMagicWand } from "react-icons/im";
import slugify from "slugify";
import { SeoSuggestButton } from "../../components/seo-suggestion-button";
import type { CategoryGetResponse } from "@/app/api/read/category/query";

export type FV = Omit<UpdateBlogInput["blog"], "categories"> &
  UpdateBlogInput["content"] & {
    categories: string[];
  };

export function EditContentPage() {
  const navigate = useNavigate();

  const [form] = Form.useForm<FV>();

  const { contentId = "" } = useParams<{ contentId: string }>();

  const { isPending, data } = useQuery({
    queryFn: async () => {
      const res = await fetch(
        "/api/manage/content?" + new URLSearchParams({ id: contentId }),
      );
      const data: BlogGetResponse = await res.json();
      return data;
    },
    queryKey: ["blog", contentId],
    gcTime: 100,
  });

  const { data: categories } = useQuery({
    queryFn: async () => {
      const res = await fetch("/api/read/category", { cache: "no-store" });
      const data: CategoryGetResponse = await res.json();
      return data;
    },
    queryKey: ["categories"],
  });

  const mutation = useMutation({
    mutationFn: async (v: FV) => {
      const newData: UpdateBlogInput = {
        blog: {
          id: Blog?.id,
          articleDate: v.articleDate ? new Date(v.articleDate) : undefined,
          isPremium: v.isPremium,
          isPublished: v.isPublished,
          slug: v.slug,
          categories: { set: v.categories?.map((id) => ({ id })) },
        },
        content: {
          id: data?.id,
          title: v.title,
          imageUrl: v.imageUrl,
          seoDescription: v.seoDescription,
          content: v.content,
          language: v.language,
          isReady: v.isReady,
        },
      };
      await fetch("/api/manage/blog/update", {
        method: "POST",
        body: JSON.stringify(newData),
      });
    },
    onSuccess: () => {
      navigate("..");
    },
  });

  const generateSlugFromTitle = () => {
    const title = form.getFieldValue("title");
    const slug = slugify(title, {
      lower: true,
      trim: true,
      locale: "en",
      remove: /[^\w\s-]/g,
    });
    form.setFieldValue("slug", slug);
  };

  const { Blog, ...contentData } = data ?? {};

  const renderForm = () => {
    if (!contentId) {
      return <div>Invalid ID input</div>;
    }

    if (isPending) {
      return <div>Loading...</div>;
    }

    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={mutation.mutate}
        initialValues={{
          ...Blog,
          ...contentData,
          articleDate: Blog?.articleDate
            ? new Date(Blog.articleDate).toISOString().split("T")[0]
            : undefined,
          categories: Blog?.categories.map((c) => c.id),
        }}
      >
        <div className="flex justify-between">
          <div className="flex gap-2">
            <Form.Item<FV>
              name="isPublished"
              layout="horizontal"
              valuePropName="checked"
            >
              <Checkbox>Published</Checkbox>
            </Form.Item>
            <Form.Item<FV>
              name="isPremium"
              layout="horizontal"
              valuePropName="checked"
            >
              <Checkbox>Premium</Checkbox>
            </Form.Item>
          </div>
          <Form.Item<FV> name="language">
            <Radio.Group optionType="button" disabled>
              <Radio value="en">English</Radio>
              <Radio value="ja">日本語</Radio>
              <Radio value="zh">中文</Radio>
            </Radio.Group>
          </Form.Item>
        </div>

        <div className="flex gap-8 flex-wrap">
          <Form.Item<FV> name="imageUrl" label="Banner image">
            <UploadImage />
          </Form.Item>
          <div className="flex-grow">
            <Form.Item<FV>
              name="title"
              label="Title"
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>
            <div className="flex gap-4">
              <Form.Item<FV>
                name="articleDate"
                label="Article date"
                className="w-full"
              >
                <Input type="date" />
              </Form.Item>
              <Form.Item<FV>
                name="categories"
                label="Category"
                className="w-full"
              >
                <Select
                  mode="multiple"
                  options={
                    categories?.map((c) => ({
                      label: c.labels.find((l) => l.language === "en")?.label,
                      value: c.id,
                    })) ?? []
                  }
                />
              </Form.Item>
            </div>
          </div>
        </div>

        <Form.Item<FV>
          name="content"
          label="Content"
          rules={[{ required: true }]}
        >
          <ContentField blogId={Blog?.id} />
        </Form.Item>

        <Form.Item<FV> name="seoDescription" label="SEO description">
          <Input.TextArea />
        </Form.Item>
        <Form.Item<FV> name="slug" label="URL slug">
          <Input suffix={<ImMagicWand onClick={generateSlugFromTitle} />} />
        </Form.Item>

        <div className="flex justify-between sticky bottom-0 bg-background border-t pt-4">
          <div className="flex gap-4">
            <SeoSuggestButton form={form} />
          </div>

          <div className="flex gap-4">
            <Form.Item<FV> name="isReady" layout="horizontal">
              <Radio.Group optionType="button">
                <Radio value={false}>Draft</Radio>
                <Radio value={true}>Ready</Radio>
              </Radio.Group>
            </Form.Item>
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </div>
        </div>
      </Form>
    );
  };

  return (
    <>
      <Typography.Title className="flex items-center gap-4">
        <Link to="..">
          <LeftCircleOutlined />
        </Link>
        <span>Edit article</span>
      </Typography.Title>
      {renderForm()}
    </>
  );
}
