import { BlogListResponse } from "@/app/api/manage/blog/list/route";
import { DeleteOutlined } from "@ant-design/icons";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Modal,
  Space,
  Table,
  TableColumnsType,
  Typography,
} from "antd";
import { useState } from "react";
import { RenderTitle } from "./render-title";
import { TranslateButton } from "./translate-button";

const fetchArticleList = async (): Promise<BlogListResponse> => {
  const data = await fetch("/api/manage/blog/list", { method: "GET" });
  return data.json();
};

export const ArticleList = () => {
  const queryClient = useQueryClient();
  const { isPending, data } = useQuery({
    queryKey: ["articleList"],
    queryFn: fetchArticleList,
  });

  const revalidateList = () =>
    queryClient.invalidateQueries({
      queryKey: ["articleList"],
      exact: true,
    });

  const [operationId, setOperationId] = useState<string | null>(null);

  const columns: TableColumnsType<BlogListResponse[0]> = [
    {
      title: "Title",
      dataIndex: "contents",
      render: (_, record: BlogListResponse[0]) => {
        return <RenderTitle record={record} />;
      },
    },
    {
      title: "Premium",
      width: 100,
      dataIndex: "isPremium",
      render: (value) => {
        return value ? "Premium" : "";
      },
    },
    {
      title: "Published",
      width: 120,
      className: "whitespace-nowrap",
      dataIndex: "isPublished",
      render: (value) => {
        return value ? (
          <Typography.Text type="success">● Published</Typography.Text>
        ) : (
          <Typography.Text type="secondary">○ Draft</Typography.Text>
        );
      },
    },
    {
      title: "Article Date",
      width: 120,
      className: "whitespace-nowrap",
      dataIndex: "articleDate",
      sorter: (a, b) =>
        new Date(a.articleDate).getTime() - new Date(b.articleDate).getTime(),

      render: (date) => {
        return new Date(date).toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
      },
    },
    {
      title: "Last Updated",
      width: 150,
      className: "whitespace-nowrap",
      dataIndex: "updatedAt",
      sorter: (a, b) =>
        new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
      render: (date) =>
        new Date(date).toLocaleDateString("en-US", {
          year: "2-digit",
          month: "2-digit",
          day: "2-digit",
          hour: "numeric",
          minute: "numeric",
          hour12: false,
        }),
    },
    {
      title: "Actions",
      width: 80,
      dataIndex: "id",
      align: "right",
      render: (id, record) => {
        return (
          <Space>
            <TranslateButton record={record} onSuccess={revalidateList} />
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                setOperationId(id);
              }}
            />
          </Space>
        );
      },
    },
  ];

  const scrollWidth = columns.reduce(
    (acc, { width }) => acc + (Number(width) || 0),
    300,
  );

  return (
    <>
      <Modal
        title="Are you sure you want to delete this article including all translations?"
        open={!!operationId}
        onCancel={() => setOperationId(null)}
        okButtonProps={{ danger: true }}
        onOk={async () => {
          await fetch(`/api/manage/blog/delete`, {
            method: "POST",
            body: JSON.stringify({ id: operationId }),
          });
          setOperationId(null);
          revalidateList();
        }}
      />
      <Table
        loading={isPending}
        dataSource={data}
        columns={columns}
        pagination={false}
        scroll={{ x: scrollWidth }}
        rowKey="id"
      />
    </>
  );
};
