{"name": "content-display-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000 --turbo", "build": "next build", "start": "next start --port 3000", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@next/third-parties": "^15.1.5", "@radix-ui/react-icons": "^1.3.2", "@stripe/stripe-js": "^4.10.0", "highlight.js": "^11.10.0", "next": "^15.4.5", "next-auth": "^5.0.0-beta.29", "react": "^19", "react-dom": "^19", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "slugify": "^1.6.6", "stripe": "^17.7.0", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^19", "eslint": "^8", "eslint-config-next": "^15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "3.3.3", "prisma-db": "workspace:*", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-config": "workspace:*", "ui": "workspace:*"}}